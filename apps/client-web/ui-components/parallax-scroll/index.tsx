"use client";
import classNames from "classnames";
import {
  PropsWithChildren,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from "react";
import style from "./index.module.scss";

export const ParallaxScroll = (
  props: PropsWithChildren<{ items: ReactNode[] }>
) => {
  const { items = [] } = props;
  const [activeId, setActiveId] = useState(-1);

  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = ref.current;
    if (!container) return;

    const handleOnScroll = () => {
      const containerRect = container.getBoundingClientRect();

      if (containerRect.top >= window.innerHeight * (1 / 3)) {
        setActiveId(-1);
        return;
      }

      if (containerRect.bottom <= window.innerHeight * (2 / 3)) {
        setActiveId(items.length);
        return;
      }

      let currentActiveId = -1;
      container.childNodes.forEach((element) => {
        if (element instanceof HTMLElement) {
          const rect = element?.getBoundingClientRect();
          const isInView = rect.top <= window.innerHeight && rect.bottom >= 0;

          if (isInView) {
            const id = element.getAttribute("data-idx");
            currentActiveId = Number(id);
          }
        }
      });
      setActiveId(Number(currentActiveId));
    };

    window.addEventListener("scroll", handleOnScroll);
    return () => {
      window.removeEventListener("scroll", handleOnScroll);
    };
  }, [items.length]);

  return (
    <div className="flex flex-col relative -z-10" ref={ref}>
      {items.map((items, idx) => {
        return (
          <ParallaxScrollItem
            key={idx}
            id={idx}
            activeId={activeId}
            onElementInView={setActiveId}
          >
            {items}
          </ParallaxScrollItem>
        );
      })}
    </div>
  );
};

interface ParallaxScrollItemProps {
  id: number;
  activeId: number;
  onElementInView: (id: number) => void;
}

export const ParallaxScrollItem = (
  props: PropsWithChildren<ParallaxScrollItemProps>
) => {
  const { children, id, activeId } = props;

  return (
    <div className="h-screen relative" data-idx={id}>
      <div
        className={classNames("w-full", style.default, {
          [style.inactive]: activeId < id,
          [style.passed]: activeId > id,
        })}
      >
        {children}
      </div>
    </div>
  );
};
