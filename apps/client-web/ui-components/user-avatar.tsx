"use client";
import { CaretDownIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { useIsSmallScreen } from "../hooks/use-screen";
import { useAuth } from "@/hooks/use-auth";
import { NavMenu } from "@repo/ui/nav-menu";
import { LinkConfig, renderLink } from "./nav-link/utils";
import { useTranslations } from "next-intl";
import { goToLogout } from "@/utils/logout";

export const UserAvatar = ({ linkConfig }: { linkConfig: LinkConfig[] }) => {
  const { isAuthed, userData } = useAuth({ requireAuth: true });
  const t = useTranslations("Nav");

  const isSmall = useIsSmallScreen();
  const firstName = userData.firstName ?? "";
  const lastName = userData.lastName ?? "";

  if (!isAuthed) return null;

  const config = [
    ...(isSmall ? linkConfig : []),

    {
      href: "/settings",
      label: t("settings"),
    },
    {
      href: "#",
      label: t("logout"),
      onClick: goToLogout,
    },
  ];

  return (
    <div className="flex gap-2 items-center">
      <NavMenu linkConfig={config.map(renderLink)}>
        <Button
          radius="full"
          variant="soft"
          color="gray"
          style={{ background: "none" }}
        >
          <div className="flex gap-2 items-center">
            <span>
              {firstName} {lastName}
            </span>
            <CaretDownIcon />
          </div>
        </Button>
      </NavMenu>
    </div>
  );
};
