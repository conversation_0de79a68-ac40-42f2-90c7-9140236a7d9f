import { useEffect, useRef, useState } from "react";

export const useContainerMargin = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerMargin, setContainerMargin] = useState(0);

  useEffect(() => {
    const updateContainerMargin = () => {
      if (containerRef.current) {
        const firstChild = containerRef.current
          .firstElementChild as HTMLElement | null;
        console.log(firstChild);
        setContainerMargin(firstChild?.offsetLeft ?? 0);
      }
    };

    updateContainerMargin();

    window.addEventListener("resize", updateContainerMargin);
    return () => {
      window.removeEventListener("resize", updateContainerMargin);
    };
  });

  return { containerMargin, containerRef };
};
