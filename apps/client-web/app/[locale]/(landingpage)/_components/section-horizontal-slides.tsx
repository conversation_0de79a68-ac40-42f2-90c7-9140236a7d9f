"use client";
import { Container } from "@/ui-components/layout/container";
import classNames from "classnames";
import Image from "next/image";

import style from "./style.module.scss";
import { useContainerMargin } from "@/utils/container-margin";

interface SectionHorizontalSlidesProps {
  title: string;
  description?: string;
  backgroundImage: string;
  slides: {
    image: string;
    title: string;
    description: string;
  }[];
}

export const SectionHorizontalSlides = ({
  title,
  description,
  backgroundImage,
  slides,
}: SectionHorizontalSlidesProps) => {
  const { containerMargin, containerRef } = useContainerMargin();

  return (
    <div
      className={classNames(
        "py-15 md:py-20 bg-black text-white bg-cover bg-center bg-no-repeat",
        "flex flex-col gap-8 md:gap-10",
      )}
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <Container>
        <div className="section-content-2 flex flex-col gap-3 md:flex-row md:justify-between">
          <h2 className={style.heading2}>{title}</h2>
          {description && (
            <p className={classNames(style.paragraph, "max-w-[392px]")}>
              {description}
            </p>
          )}
        </div>
      </Container>
      <Container className="overflow-scroll no-scrollbar" ref={containerRef}>
        <div className="section-content-2">
          <div className="overflow-visible">
            <div className="flex gap-6">
              {slides.map((slide) => {
                return (
                  <div
                    key={slide.title}
                    className="p-4 rounded rounded-lg w-72 min-w-72 bg-white/10 shrink-0 flex flex-col gap-2"
                  >
                    <Image
                      unoptimized
                      src={slide.image}
                      alt={slide.title}
                      width={80}
                      height={80}
                    />
                    <h3 className={style.heading3}>{slide.title}</h3>
                    <p className={style.paragraph2}>{slide.description}</p>
                  </div>
                );
              })}
              <div style={{ paddingRight: containerMargin }} />
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};
