import { Link } from "@/i18n/navigation";
import classNames from "classnames";
import Image from "next/image";
import style from "./style.module.scss";
import type { LinkConfig } from "@/ui-components/nav-link/utils";

export const LandingPageFooter = ({ config }: { config: LinkConfig[] }) => {
  return (
    <div
      className={classNames(
        "text-sm divide-y divide-gray-300 md:divide-none",
        "py-10 flex flex-col md:flex-row-reverse md:justify-between",
      )}
    >
      <div className="flex flex-col gap-4 pb-4 md:pb-0 md:flex-row">
        {config.map(({ href, label }) => {
          return (
            <Link key={href} href={href} className="font-medium">
              {label}
            </Link>
          );
        })}
      </div>
      <div
        className={classNames(
          style.textGray,
          "pt-4 md:pt-0 flex flex-col gap-4",
        )}
      >
        <Image
          unoptimized
          src="/odc-logo-bw.png"
          alt="DSC logo"
          width={65}
          height={32}
        />
        <span className="hidden md:block">Contact <NAME_EMAIL></span>
        <div className="flex flex-col gap-2 md:flex-row md:gap-10">
          <p>© ODC 2018–2025. All rights reserved</p>
          <div className={style.smallLinks}>
            <Link href="/privacy">Privacy</Link>
            <Link href="/terms">Terms</Link>
          </div>
        </div>
      </div>
    </div>
  );
};
