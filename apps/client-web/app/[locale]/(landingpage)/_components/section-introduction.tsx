import { Container } from "@/ui-components/layout/container";
import style from "./style.module.scss";
import classNames from "classnames";
import { PrimaryButton } from "@/ui-components/buttons";
import Image from "next/image";
import { Link } from "@/i18n/navigation";

interface Introduction {
  title: string;
  description: string;
  image: string;
  href: string;
}

interface SectionIntroductionProps {
  introductions: Introduction[];
  learnMoreText: string;
  className?: string;
}

export const SectionIntroduction = ({
  introductions,
  learnMoreText,
  className,
}: SectionIntroductionProps) => {
  return (
    <Container className={classNames("py-10 md:py-30", className)}>
      <div className="section-content flex flex-col gap-10 xl:gap-30">
        {introductions.map((section, idx) => {
          return (
            <div
              key={idx}
              className={classNames(
                "flex flex-col lg:flex-row gap-10 items-center",
                {
                  ["lg:flex-row-reverse"]: idx % 2 === 1,
                },
              )}
            >
              <div className="flex-1 flex flex-col gap-6">
                <h2 className={classNames(style.textOrange, style.heading2)}>
                  {section.title}
                </h2>
                <p
                  className={classNames(
                    style.paragraph,
                    style.textGray,
                    "whitespace-pre-line",
                  )}
                >
                  {section.description}
                </p>

                <div>
                  <PrimaryButton size="3" asChild>
                    <Link className="mr-auto" href={section.href}>
                      {learnMoreText}
                    </Link>
                  </PrimaryButton>
                </div>
              </div>
              <div className="flex-1 shrink-0">
                <Image
                  unoptimized
                  src={section.image}
                  alt=""
                  width={750}
                  height={500}
                  className="lg:w-full aspect-3/2 object-cover"
                />
              </div>
            </div>
          );
        })}
      </div>
    </Container>
  );
};
