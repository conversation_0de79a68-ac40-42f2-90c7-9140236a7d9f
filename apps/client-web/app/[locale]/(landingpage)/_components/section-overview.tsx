import { Container } from "@/ui-components/layout/container";
import classNames from "classnames";
import Image from "next/image";
import style from "./style.module.scss";

interface Feature {
  icon: string;
  title: string;
  description: string;
}

interface SectionOverviewProps {
  title: string;
  features: Feature[];
  className?: string;
}

export const SectionOverview = ({
  title,
  features,
  className,
}: SectionOverviewProps) => {
  return (
    <Container
      className={classNames(
        "bg-[#78B4D2] text-white py-20 md:py-30",
        className,
      )}
    >
      <div className="section-content-2">
        <h2
          className={classNames(style.heading2, "md:text-center mb-8 md:mb-10")}
        >
          {title}
        </h2>
        <div
          className={classNames(
            "grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
          )}
        >
          {features.map((feature, index) => {
            return (
              <div key={index} className="flex gap-2 items-start md:flex-col">
                <Image
                  unoptimized
                  className="w-14 h-14 md:w-20 md:h-20"
                  src={feature.icon}
                  height={80}
                  width={80}
                  alt={feature.title}
                />
                <div className="flex flex-col gap-2">
                  <h3 className={classNames(style.heading3, "font-medium")}>
                    {feature.title}
                  </h3>
                  <p className={style.paragraph2}>{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </Container>
  );
};
