import { Container } from "@/ui-components/layout/container";
import classNames from "classnames";
import style from "./style.module.scss";

interface SectionSingleIntroProps {
  backgroundImage?: string;
  title: string;
  description: string;
  button?: React.ReactNode;
  theme?: "light" | "dark";
}

export const SectionSingleIntro = ({
  backgroundImage,
  title,
  description,
  button,
  theme = "light",
}: SectionSingleIntroProps) => {
  const isDark = theme === "dark";
  return (
    <Container
      className={classNames("h-[812px] flex flex-col relative w-full -z-10")}
      background={
        <>
          <div
            style={{ backgroundImage: `url(${backgroundImage})` }}
            className={classNames(
              isDark ? "w-full" : "w-full md:w-1/2",
              "bg-cover bg-right absolute top-0 right-0 l-0 h-full -z-10",
            )}
          />
          <div
            className={classNames(
              "bg-linear-to-t from-[#000000] to-[#00000000] md:bg-none",
              isDark ? "md:bg-black/50" : "md:bg-[#F2F2F2]",
              "w-full md:w-1/2 absolute bottom-0 left-0 h-96 md:h-full -z-10",
            )}
          />
        </>
      }
    >
      <div className="h-[812px] py-14 col-span-4 md:col-span-6 flex flex-col justify-end md:justify-center gap-10 xl:gap-30">
        <div className="flex flex-col lg:flex-row gap-10 items-center">
          <div className="flex-1 flex flex-col gap-6">
            <h2
              className={classNames(style.heading2, {
                "text-background": isDark,
                [style.textOrange]: !isDark,
                [style.smWhite]: !isDark,
              })}
            >
              {title}
            </h2>
            <p
              className={classNames(
                style.paragraph,
                {
                  "text-background": isDark,
                  [style.textGray]: !isDark,
                  [style.smWhite]: !isDark,
                },
                "whitespace-pre-line",
              )}
            >
              {description}
            </p>
            <div className="empty:hidden">{button}</div>
          </div>
        </div>
      </div>
    </Container>
  );
};
