"use client";
import { Container } from "@/ui-components/layout/container";
import style from "./style.module.scss";
import classNames from "classnames";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useContainerMargin } from "@/utils/container-margin";

const PRESS_IMAGES = [
  "/press/press-emblem-of-oecusse.png",
  "/press/press-odc.png",
  "/press/press-benzinga.png",
  "/press/press-bussiness-insider.png",
  "/press/press-msn.png",
  "/press/press-barchart.png",
  "/press/press-the-global-and-mail.png",
  "/press/press-digital-journal.png",
];

export const SectionPress = () => {
  const t = useTranslations("HomePage.Press");

  const { containerMargin, containerRef } = useContainerMargin();
  return (
    <div className="py-20 md:py-30 bg-[#F2F2F2]">
      <Container>
        <div
          className={classNames(
            style.paragraph,
            style.textBlue,
            "font-medium mb-8 md:mb-10 section-content md:text-center",
          )}
        >
          <p className="mb-2">{t("subtitle")}</p>
          <h2
            className={classNames(
              style.heading2,
              style.textOrange,
              "mb-3 md:mb-6",
            )}
          >
            {t("title")}
          </h2>
          <p className={classNames(style.paragraph2, style.textGray)}>
            {t("description")}
          </p>
        </div>
      </Container>
      <Container ref={containerRef} className="overflow-auto no-scrollbar">
        <div className="col-span-full flex gap-8">
          {PRESS_IMAGES.map((src) => {
            return (
              <Image
                unoptimized
                key={src}
                className="saturate-0 w-[184px] h-[104px] object-contain"
                src={src}
                width={184}
                height={104}
                alt=""
              />
            );
          })}
          <div style={{ paddingRight: containerMargin }} />
        </div>
      </Container>
    </div>
  );
};
