"use client";
import service from "@/api";
import { API_BASE_URL } from "@/app/constants";
import { Button } from "@radix-ui/themes";
import Cookies from "js-cookie";

const Page = () => {
  return (
    <div className="p-12">
      <div className="flex flex-col gap-4 items-center">
        <Button
          onClick={async () => {
            fetch(`${API_BASE_URL}/test`, {
              method: "POST",
              headers: {
                "X-XSRF-TOKEN": Cookies.get("XSRF-TOKEN") ?? "",
              },
              credentials: "include",
            });
          }}
        >
          POST test
        </Button>
        <Button
          onClick={async () => {
            const res = await fetch(`${API_BASE_URL}/test`, {
              method: "GET",
              credentials: "include",
            });
            const data = await res.json();
            console.log(data);
          }}
        >
          GET test
        </Button>

        <Button
          onClick={async () => {
            const profile = await service.getUserProfile();
            console.log(profile);
          }}
        >
          GET user profiles
        </Button>
      </div>
    </div>
  );
};

export default Page;
