import { Container } from "@/ui-components/layout/container";
import classNames from "classnames";
import Image from "next/image";
import { useTranslations } from "next-intl";

import style from "../_components/style.module.scss";

export const SectionInvestmentManaged = () => {
  const t = useTranslations("InvestPage.InvestmentManaged");

  const items = Array.from({ length: 2 }).map((_, index) => ({
    icon: t(`wallets.${index}.icon`),
    title: t(`wallets.${index}.title`),
    description: t(`wallets.${index}.description`),
  }));

  return (
    <Container className="py-15 md:py-20">
      <div className="col-span-full lg:col-start-2 lg:col-span-3 mb-8">
        <h2 className={classNames(style.heading2, style.textOrange)}>
          {t("title")}
        </h2>
      </div>
      <div className="col-span-full lg:col-span-7 flex flex-col md:flex-row gap-8 md:gap-6">
        {items.map(({ icon, title, description }) => {
          return (
            <div
              key={title}
              className="flex flex-col bg-[#F2F2F2] p-4 rounded-md"
            >
              <Image
                unoptimized
                src={icon}
                alt={title}
                width={120}
                height={120}
                className="mb-6"
              />

              <h3 className={classNames("mb-2", style.heading2)}>{title}</h3>
              <p className={classNames(style.paragraph2, style.textGray)}>
                {description}
              </p>
            </div>
          );
        })}
      </div>
    </Container>
  );
};
