"use client";
import { Container } from "@/ui-components/layout/container";
import { ProfileSection } from "./profile-section";
import { ArrowRightIcon, InfoCircledIcon } from "@radix-ui/react-icons";
import { <PERSON><PERSON>, Card, Heading, Text, Tooltip } from "@radix-ui/themes";
import { AssetValues } from "./_components/asset-values";
import { TransactionTable } from "../transaction-history/_components/transaction-table";

import { Link } from "@/i18n/navigation";
import { useAuth } from "@/hooks/use-auth";
import { SecondaryButton } from "@repo/ui/form-button";
import { useTranslations } from "next-intl";
import { useQuery } from "@tanstack/react-query";
import service from "@/api";
import { KycGate } from "../_components/kyc-gate";

function Dashboard() {
  const { userData } = useAuth();
  const t = useTranslations("Portal.Dashboard");

  const { data, isLoading } = useQuery({
    queryFn: service.getDashboardInfo,
    queryKey: ["dashboard"],
  });

  const {
    recentTransactions: records = [],
    userInfo,
    accountSummary,
  } = data?.data?.data ?? {};

  if (!userData) return null;

  return (
    <main className="grow py-6 md:py-10 bg-[#F2F2F2]">
      <Container>
        <div className="section-content flex flex-col gap-y-6">
          <ProfileSection profile={userInfo} />
          <Card size={{ initial: "2", md: "3" }}>
            <div className="flex flex-col gap-y-4">
              {/* header */}
              <div className="flex flex-col gap-y-4 md:flex-row justify-between md:items-start">
                <div className="flex flex-col gap-y-2">
                  <div className="flex items-center">
                    <Heading as="h3" size="5" weight="bold">
                      {t("totalBalance")}
                    </Heading>
                    <Tooltip content={t("totalBalanceTooltip")} side="right">
                      <InfoCircledIcon
                        className="ml-2 text-gray-500"
                        width={16}
                        height={16}
                      />
                    </Tooltip>
                  </div>
                  <div className="flex items-center gap-x-2">
                    <Text
                      className="color-primary"
                      size={{ initial: "7", sm: "8" }}
                      weight="bold"
                    >
                      {accountSummary?.totalBalance}
                    </Text>
                    <Text size="3" weight="medium">
                      {accountSummary?.currency}
                    </Text>
                  </div>
                </div>

                <Button color="orange" size="3" radius="full" asChild>
                  <Link href="/deposit">{t("depositFunds")}</Link>
                </Button>
              </div>
              {/* chart */}
              <div className="flex flex-col gap-y-2">
                <Heading as="h3" size="5" weight="bold">
                  {t("myAssets")}
                </Heading>
                <div className="flex flex-col  gap-y-4">
                  <AssetValues
                    main={{ name: t("crypto"), value: "-" }}
                    details={[
                      { name: t("usdt"), value: "-" },
                      { name: t("usdc"), value: "-" },
                    ]}
                  />
                  <AssetValues
                    main={{ name: t("cash"), value: "-" }}
                    details={[{ name: t("usd"), value: "-" }]}
                  />
                </div>
              </div>
            </div>
          </Card>
          <Card size={{ initial: "2", md: "3" }}>
            <div className="flex flex-col gap-y-4">
              <div className="flex justify-between items-center">
                <Heading as="h3" size="4" weight="bold">
                  {t("recentTransactions")}
                </Heading>
                <SecondaryButton asChild size="3">
                  <Link href="/transaction-history">
                    {t("viewMore")} <ArrowRightIcon />
                  </Link>
                </SecondaryButton>
              </div>

              <TransactionTable data={records} disableActions isLoading={isLoading} />
            </div>
          </Card>
        </div>
      </Container>
    </main>
  );
}

export default function DashboardPage() {
  return (
    <KycGate type="route">
      <Dashboard />
    </KycGate>
  );
}
