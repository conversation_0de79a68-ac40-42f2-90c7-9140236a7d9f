"use client";
import { useAuth } from "@/hooks/use-auth";
import { Heading } from "@radix-ui/themes";
import { CopyButton } from "@/ui-components/copy-button";
import { ProfileListItem } from "@repo/ui/profile-list-item";
import { Container } from "@repo/ui/layout/container";
import { useTranslations } from "next-intl";
import { UserStatus } from "../dashboard/profile-section";
import { KycGate } from "../_components/kyc-gate";

function Settings() {
  const { userData } = useAuth();
  const t = useTranslations("Portal.Settings");

  if (!userData) return null;

  const { publicId, email, userStatus } = userData;

  const firstName = userData.firstName ?? "";
  const lastName = userData.lastName ?? "";

  return (
    <main className="grow py-6 md:py-14 bg-[#F2F2F2]">
      <Container>
        <div className="col-span-full md:col-span-8 md:col-start-3 flex flex-col">
          <div className="mb-8">
            <Heading as="h3" size="7" weight="bold">
              {firstName} {lastName}
            </Heading>
          </div>

          <ProfileListItem label={t("email")} value={email} />

          <ProfileListItem
            label={t("profileId")}
            value={
              <div className="flex gap-2 items-center">
                {publicId} {publicId && <CopyButton text={publicId} />}
              </div>
            }
          />

          <ProfileListItem
            label={t("identityVerification")}
            value={<UserStatus userStatus={userStatus} />}
          />

          {/* <ProfileListItem
            label={t("changePassword")}
            value={
              <Button size="1" radius="full" color="gray" variant="soft">
                {t("changePassword")}
              </Button>
            }
          /> */}
        </div>
      </Container>
    </main>
  );
}

export default function SettingsPage() {
  return (
    <KycGate type="route">
      <Settings />
    </KycGate>
  );
}
