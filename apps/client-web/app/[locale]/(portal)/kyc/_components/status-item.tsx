"use client";
import { CheckCircledIcon } from "@radix-ui/react-icons";
import { Badge, Heading } from "@radix-ui/themes";
import classNames from "classnames";
import Image from "next/image";
import { useTranslations } from "next-intl";

export interface StatusItemProps {
  icon: string;
  title: string;
  status: "complete" | "pending" | null;
}

export const StatusItem = ({ icon, title, status }: StatusItemProps) => {
  const t = useTranslations("Portal.Profile.status");

  const renderStatus = () => {
    switch (status) {
      case "complete":
        return (
          <CheckCircledIcon height={20} width={20} className="color-primary" />
        );
      case "pending":
        return (
          <Badge radius="full" color="orange">
            {t("pending")}
          </Badge>
        );
      default:
        return <div className="w-5 h-5"></div>;
    }
  };

  return (
    <>
      <div className="flex gap-6 items-center min-h-6">
        <Image
          unoptimized
          src={icon}
          alt="verify"
          width={56}
          height={56}
          className={classNames("p-1 hidden md:block", {
            ["saturate-0"]: !status,
          })}
        />
        <Heading
          as="h5"
          size={{ initial: "3", md: "5" }}
          weight="medium"
          className="grow-1"
          color={!status ? "gray" : undefined}
        >
          {title}
        </Heading>
        {renderStatus()}
      </div>
      <div className="last:hidden py-0.5">
        <div
          className={classNames(
            "w-7 h-3 md:h-5 border-r-1 md:border-r-2 border-gray-400",
            {
              "border-orange-500": status === "complete",
              "border-dashed": status === "pending",
            },
          )}
        ></div>
      </div>
    </>
  );
};
