"use client";
import { useAuth } from "@/hooks/use-auth";
import { KycSteps } from "./_components/kyc-steps";
import { redirect } from "next/navigation";
import { useKycStatus } from "@/hooks/use-kyc-status";

const Page = () => {
  useAuth({ requireAuth: true });
  const { kycStatus } = useKycStatus();

  if (kycStatus === undefined) {
    return null;
  }

  if (kycStatus !== null && kycStatus !== "PENDING") {
    redirect("/");
  }

  return (
    <main className="flex-1 flex flex-col gpa-6 bg-[#F2F2F2]">
      <KycSteps />
    </main>
  );
};

export default Page;
