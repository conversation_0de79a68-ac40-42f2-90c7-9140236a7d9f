"use client";
import { useKycStatus } from "@/hooks/use-kyc-status";
import { redirect } from "next/navigation";
import { PropsWithChildren } from "react";

export type GateType = "component" | "route";

export const KycGate = ({
  children,
  type,
}: PropsWithChildren<{ type: GateType }>) => {
  const { kycStatus, isKycApproved } = useKycStatus();

  if (kycStatus === undefined) {
    return null;
  }

  if (!isKycApproved) {
    if (type === "component") {
      return null;
    }
    if (type === "route") {
      return redirect("/kyc");
    }
  }

  return children;
};
