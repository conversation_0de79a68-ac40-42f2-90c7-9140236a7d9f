/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  PagedRequest,
  ResponseResultObject,
  ResponseResultPagedUserSummaryResponse,
  ResponseResultPortfolioOverviewResponse,
  ResponseResultVoid,
} from "./data-contracts";
import { HttpClient, RequestParams } from "./http-client";

export class Api<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags V1UserManagementController
   * @name GetUsers
   * @request GET:/api/v1/user-management/users
   */
  getUsers = (
    query: {
      publicId?: string;
      email?: string;
      pagedRequest: PagedRequest;
    },
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultPagedUserSummaryResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/user-management/users`,
      method: "GET",
      query: query,
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DashboardController
   * @name GetPortfolioOverview
   * @request GET:/api/v1/dashboard
   */
  getPortfolioOverview = (params: RequestParams = {}) =>
    this.request<
      ResponseResultPortfolioOverviewResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/dashboard`,
      method: "GET",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1AuthController
   * @name Logout
   * @request GET:/api/v1/auth/logout
   */
  logout = (params: RequestParams = {}) =>
    this.request<
      void,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/auth/logout`,
      method: "GET",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1AuthController
   * @name Login
   * @request GET:/api/v1/auth/login
   */
  login = (params: RequestParams = {}) =>
    this.request<
      void,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/auth/login`,
      method: "GET",
      ...params,
    });
}
