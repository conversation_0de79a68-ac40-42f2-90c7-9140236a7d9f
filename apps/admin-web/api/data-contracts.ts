/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface ResponseResultObject {
  /**
   * Business Code
   * @format int32
   * @default 0
   */
  code?: number;
  message?: string;
  data?: any;
  requestId?: string;
}

export interface ResponseResultVoid {
  /**
   * Business Code
   * @format int32
   * @default 0
   */
  code?: number;
  message?: string;
  data?: any;
  requestId?: string;
}

export interface PagedRequest {
  /**
   * 1-indexed page number
   * @format int32
   * @min 1
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @min 1
   * @max 100
   * @default 20
   */
  pageSize?: number;
}

/** Represents a monetary value with its amount and currency. */
export interface MoneyDTO {
  /**
   * The numerical amount of money.
   * @example 10000
   */
  amount?: number;
  /**
   * The three-letter ISO currency code.
   * @example "USD"
   */
  currency?: string;
}

export interface PagedUserSummaryResponse {
  records?: UserSummaryResponse[];
  /** @format int32 */
  totalItems?: number;
  /**
   * 1-indexed page number
   * @format int32
   * @min 1
   */
  page?: number;
  /**
   * @format int32
   * @min 1
   * @max 100
   * @default 20
   */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
}

export interface ResponseResultPagedUserSummaryResponse {
  /**
   * Business Code
   * @format int32
   * @default 0
   */
  code?: number;
  message?: string;
  data?: PagedUserSummaryResponse;
  requestId?: string;
}

export interface UserSummaryResponse {
  publicId?: string;
  email?: string;
  /** @format date-time */
  createdAt?: string;
  /** Represents a monetary value with its amount and currency. */
  totalInvestment?: MoneyDTO;
  countryOfResidence?: string;
}

export interface AllocationDTO {
  currency?: string;
  percentage?: number;
}

export interface AssetBreakdownDTO {
  asset?: string;
  /** Represents a monetary value with its amount and currency. */
  totalAmount?: MoneyDTO;
  allocation?: number;
}

export interface PortfolioOverviewResponse {
  totalInvestments?: TotalInvestmentDTO;
  allocation?: AllocationDTO[];
  breakdown?: AssetBreakdownDTO[];
}

export interface ResponseResultPortfolioOverviewResponse {
  /**
   * Business Code
   * @format int32
   * @default 0
   */
  code?: number;
  message?: string;
  data?: PortfolioOverviewResponse;
  requestId?: string;
}

export interface TotalInvestmentDTO {
  /** Represents a monetary value with its amount and currency. */
  totalAmountUSD?: MoneyDTO;
}
