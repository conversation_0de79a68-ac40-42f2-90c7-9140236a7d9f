"use client";
import { useQuery } from "@tanstack/react-query";
import service from "@/api";
import { AdminNav } from "./components/admin-nav";
import { Container } from "@repo/ui/layout/container";
import { SecondaryButton } from "@repo/ui/form-button";
import Link from "next/link";
import { API_BASE_URL } from "./constants";
import { Button } from "@radix-ui/themes";

export default function Home() {
  const { data } = useQuery({
    queryFn: async () => {
      try {
        const res = await service
          .getPortfolioOverview()
          .then((res) => res.json());

        return res.data;
      } catch {
        return undefined;
      }
    },
    queryKey: ["portfolio-overview"],
  });

  const isLogin = !!data;

  return (
    <>
      <AdminNav>
        {isLogin ? (
          <Button size="2" variant="soft" color="blue" radius="full" asChild>
            <Link href="/dashboard">Dashboard</Link>
          </Button>
        ) : (
          <Button
            size="2"
            variant="soft"
            color="blue"
            radius="full"
            onClick={() => {
              window.location.href = `${API_BASE_URL}/api/v1/auth/login`;
            }}
          >
            Login
          </Button>
        )}
      </AdminNav>
    </>
  );
}
