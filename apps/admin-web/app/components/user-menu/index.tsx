"use client";
import { API_BASE_URL } from "@/app/constants";
import { CaretDownIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { NavMenu } from "@repo/ui/nav-menu";
import Link from "next/link";

export const UserMenu = () => {
  const { isAuthed = true } = {};

  const { firstName = "Qi Zhen", lastName = "Yeoh" } = {};

  if (!isAuthed) return null;

  const config = [
    {
      item: <Link href="/settings">Settings</Link>,
      label: "Settings",
    },
    {
      item: (
        <button
          onClick={() => {
            window.location.href = `${API_BASE_URL}/api/v1/auth/logout`;
          }}
        >
          Logout
        </button>
      ),
      label: "Logout",
    },
  ];

  return (
    <div className="flex gap-2 items-center">
      <NavMenu linkConfig={config}>
        <Button
          radius="full"
          variant="soft"
          color="gray"
          style={{ background: "none" }}
        >
          <div className="flex gap-2 items-center">
            <span>
              {firstName} {lastName}
            </span>
            <CaretDownIcon />
          </div>
        </Button>
      </NavMenu>
    </div>
  );
};
