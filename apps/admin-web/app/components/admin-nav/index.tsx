"use client";
import { PropsWithChildren } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { TabNav } from "@radix-ui/themes";
import { useMediaQuery } from "@repo/ui/hooks/use-media-query";
import { SideMenu, SideMenuConfig } from "./side-menu";
import style from "./index.module.scss";

export const AdminNav = ({
  children,
  config = [],
}: PropsWithChildren<{ config?: SideMenuConfig[] }>) => {
  const pathname = usePathname();
  const isLarge = useMediaQuery("(width >= 64rem)");

  return (
    <div className="border-b border-gray-200">
      <div className="max-w-[1270px] mx-auto grid-cols-12 gap-4 px-6">
        <nav className="col-span-full h-18 md:h-15 flex items-center justify-between">
          <div className="flex gap-6 items-center">
            <div className="flex items-center gap-2">
              {config.length > 0 && <SideMenu config={config} />}
              <Link href="/" className="shrink-0">
                <Image
                  unoptimized
                  src="/logo.png"
                  alt=""
                  width={109}
                  height={32}
                />
              </Link>
            </div>
            {isLarge && (
              <TabNav.Root color="gray" className={style.nav}>
                {config.map(({ href, label }) => {
                  const active = pathname.startsWith(href);

                  return (
                    <TabNav.Link
                      key={href}
                      asChild
                      active={active}
                      className={style.tab}
                    >
                      <Link key={href} href={href}>
                        <span>{label}</span>
                      </Link>
                    </TabNav.Link>
                  );
                })}
              </TabNav.Root>
            )}
          </div>
          <div>{children}</div>
        </nav>
      </div>
    </div>
  );
};
