"use client";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, Heading } from "@radix-ui/themes";
import { Container } from "@repo/ui/layout/container";
import service from "@/api";
import { SearchFilters } from "./_components/search-filters";
import { UsersTable } from "./_components/users-table";
import { Pagination } from "@repo/ui/pagination";
import { DownloadIcon } from "@radix-ui/react-icons";
import { InfoLayout } from "@repo/ui/info-layout";

const Page = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const { data, isLoading, error } = useQuery({
    queryKey: ["users", searchQuery, currentPage, pageSize],
    queryFn: async () => {
      const searchParams: {
        publicId?: string;
        email?: string;
        pagedRequest: {
          page: number;
          pageSize: number;
        };
      } = {
        pagedRequest: {
          page: currentPage,
          pageSize,
        },
      };

      // Determine if search query is email or portfolio ID
      if (searchQuery.trim()) {
        if (searchQuery.includes("@")) {
          searchParams.email = searchQuery.trim();
        } else {
          searchParams.publicId = searchQuery.trim();
        }
      }

      const response = await service
        .getUsers(searchParams)
        .then((res) => res.json());
      return response.data;
    },
  });

  const users = data?.records || [];
  const totalPages = data?.totalPages || 1;

  const handleExportData = () => {
    // TODO: Implement export functionality
    console.log("Export data functionality to be implemented");
  };

  return (
    <main className="grow py-6 md:py-14">
      <Container>
        <div className="col-span-full flex justify-between items-center mb-6">
          <Heading size="7" weight="medium">
            <div>User management</div>
          </Heading>
          <Button
            radius="full"
            color="gray"
            highContrast
            variant="soft"
            size="2"
            onClick={handleExportData}
          >
            <DownloadIcon />
            Export data
          </Button>
        </div>

        <div className="col-span-full mb-6">
          <SearchFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
        </div>

        <div className="col-span-full">
          {error ? (
            <InfoLayout
              className="py-10"
              icon="/empty-file.png"
              iconAlt="no data"
              title="Error"
              description="An error occurred while fetching users"
            />
          ) : (
            <>
              <UsersTable data={users} isLoading={isLoading} />
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  className="justify-end"
                  nextText="Next"
                  previousText="Previous"
                />
              )}
            </>
          )}
        </div>
      </Container>
    </main>
  );
};

export default Page;
