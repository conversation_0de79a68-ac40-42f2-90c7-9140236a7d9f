"use client";

import { But<PERSON>, Head<PERSON> } from "@radix-ui/themes";
import { Container } from "@repo/ui/layout/container";
import { ProfileListItem } from "@repo/ui/profile-list-item";

export default function ProfileOverviewPage() {
  const {
    firstName = "<PERSON>hen",
    lastName = "Yeoh",
    role = "admin",
    email = "<EMAIL>",
  } = {};

  const roleDisplay = { admin: "Admin" }[role];

  return (
    <main className="grow py-6 md:py-14">
      <Container>
        <div className="col-span-full md:col-span-8 md:col-start-3 flex flex-col">
          <div className="mb-8">
            <Heading as="h3" size="7" weight="bold">
              {firstName} {lastName}
            </Heading>
          </div>

          <ProfileListItem label="Email" value={email} />

          <ProfileListItem label="Role" value={roleDisplay} />

          <ProfileListItem
            label="Password"
            value={
              <Button size="1" radius="full" color="gray" variant="soft">
                Change password
              </Button>
            }
          />
        </div>
      </Container>
    </main>
  );
}
