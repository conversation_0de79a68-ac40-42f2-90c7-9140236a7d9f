import { DropdownMenu } from "@radix-ui/themes";
import { PropsWithChildren, ReactNode } from "react";

export const AppMenu = ({
  children,
  config,
}: PropsWithChildren<{
  config: { key: string; label: ReactNode; onClick?: () => void }[];
}>) => {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>{children}</DropdownMenu.Trigger>
      <DropdownMenu.Content side="bottom" align="end">
        {config.map(({ key, label, onClick }) => {
          return (
            <DropdownMenu.Item key={key} onClick={onClick}>
              {label}
            </DropdownMenu.Item>
          );
        })}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
